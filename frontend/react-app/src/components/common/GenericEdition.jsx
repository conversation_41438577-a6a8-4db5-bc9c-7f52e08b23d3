import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import GenericForm from "./GenericForm";

export const GenericEdition = ({ modelName, apiGet, apiUpdate, schema, fields, navigateTo }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [defaultValues, setDefaultValues] = useState({});

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await apiGet(id);
        setDefaultValues(data);
      } catch (error) {
        console.error(`Error fetching ${modelName}:`, error);
      }
    };
    fetchData();
  }, [id, apiGet, modelName]);

  const handleUpdate = async (data) => {
    try {
      await apiUpdate(id, data);
      navigate(navigateTo);
    } catch (error) {
      console.error(`Error updating ${modelName}:`, error);
    }
  };

  return (
    <GenericForm
      defaultValues={defaultValues}
      onSubmit={handleUpdate}
      isEditMode={true}
      schema={schema}
      fields={fields}
      modelName={modelName}
    />
  );
};