import  familleApi  from "../../api/famille";
import { GenericCreation } from "../../components/common/GenericCreation";
import { familleSchema, familleFields, familleConfig } from "./familleConfig";

const FamilleCreate = () => {
  return (
    <GenericCreation
      modelName={familleConfig.modelName}
      apiCreate={familleApi.create}
      schema={familleSchema}
      fields={familleFields}
      navigateTo={familleConfig.navigateTo}
    />
  );
};

export default FamilleCreate;