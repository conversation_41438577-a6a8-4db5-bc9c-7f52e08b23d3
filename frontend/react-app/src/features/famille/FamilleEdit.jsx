import familleApi from "../../api/famille";
import { GenericEdition } from "../../components/common/GenericEdition";
import { familleSchema, familleFields, familleConfig } from "./FamilleConfig";

const FamilleEdit = () => {
  return (
    <GenericEdition
      modelName={familleConfig.modelName}
      apiGet={familleApi.getById}
      apiUpdate={familleApi.update}
      schema={familleSchema}
      fields={familleFields}
      navigateTo={familleConfig.navigateTo}
    />
  );
};

export default FamilleEdit;