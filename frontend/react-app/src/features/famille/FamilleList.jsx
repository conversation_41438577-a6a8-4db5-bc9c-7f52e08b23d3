import { useState } from 'react';
import familleApi from '../../api/famille';
import membreApi from '../../api/membre';
import GenericList from './../../components/common/GenericList';
import ModalAddMembre from './../../components/famille/ModalAddMembre';

const FamilleList = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [members, setMembers] = useState([]);
    const [selectedMembers, setSelectedMembers] = useState([]);
    const [selectedFamilleId, setSelectedFamilleId] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const headers = [
        { label: 'Nom', field: 'nom' },
        { label: 'Adresse', field: 'adresse' },
        { label: 'Observation', field: 'observation' },
        { label: 'Nombre de membres', field: 'mambras', isSpecial: true },
    ];

    const handleAddMembers = (familleId) => {
        setSelectedFamilleId(familleId);
        setSearchTerm('');
        setMembers([]);
        setSelectedMembers([]);
        setIsModalOpen(true);
    };

    // Handle search term change for filtering the member list
    const handleSearchChange = (e) => {
        const term = e.target.value;
        setSearchTerm(term);
    };

    const handleSelectMember = (memberId) => {
        setSelectedMembers(prev => {
            if (prev.includes(memberId)) {
                return prev.filter(id => id !== memberId);
            } else {
                return [...prev, memberId];
            }
        });
    };

    const handleRemoveSelectedMember = (memberId) => {
        setSelectedMembers(prev => prev.filter(id => id !== memberId));
    };

    const handleAddMembersToFamille = async () => {
        try {
            await familleApi.addMembers(selectedFamilleId, selectedMembers);
            setIsModalOpen(false);
            // Optionally show a success message
            // Refresh the family list to show updated member count
            window.location.reload();
        } catch (error) {
            console.error('Error adding members to family:', error);
            // Optionally show an error message
        }
    };

    // Get member by ID for display
    const getMemberById = (id) => {
        return members.find(m => m.id === id);
    };

    return (
        <div>
            <GenericList
                modelName="famille"
                api={familleApi}
                headers={headers}
                onAddMembers={handleAddMembers}
            />

            {/* Modal for adding members to a family */}
            {isModalOpen && (
                <ModalAddMembre
                    selectedFamilleId={selectedFamilleId}
                    setSelectedFamilleId={setSelectedFamilleId}
                    searchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    members={members}
                    setMembers={setMembers}
                    selectedMembers={selectedMembers}
                    setSelectedMembers={setSelectedMembers}
                    setIsModalOpen={setIsModalOpen}
                    isLoading={isLoading}
                    setIsLoading={setIsLoading}
                    handleSearchChange={handleSearchChange}
                    handleSelectMember={handleSelectMember}
                    handleRemoveSelectedMember={handleRemoveSelectedMember}
                    getMemberById={getMemberById}
                    handleAddMembersToFamille={handleAddMembersToFamille}
                    membreApi={membreApi}
                />
            )}
        </div>
    );
};

export default FamilleList;