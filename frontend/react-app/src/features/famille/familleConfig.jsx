import * as yup from "yup";

export const familleSchema = yup.object({
  nom: yup.string().required('Le nom est requis'),
  adresse: yup.string().nullable(),
  observation: yup.string().nullable(),

}).required();

export const familleFields = [
  { name: "nom", label: "Nom *", type: "text" },
  { name: "adresse", label: "Adress<PERSON>", type: "text" },
  { name: "observation", label: "Observation", type: "text" },

];

export const familleConfig = {
  modelName: "famille",
  navigateTo: "/familles",
};