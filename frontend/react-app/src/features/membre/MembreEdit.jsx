import membreApi from "../../api/membre";
import { GenericEdition } from "../../components/common/GenericEdition";
import { membreSchema, membreFields, membreConfig } from "./membreConfig";

const MembreEdit = () => {
  return (
    <GenericEdition
      modelName={membreConfig.modelName}
      apiGet={membreApi.getById}
      apiUpdate={membreApi.update}
      schema={membreSchema}
      fields={membreFields}
      navigateTo={membreConfig.navigateTo}
    />
  );
};

export default MembreEdit;
