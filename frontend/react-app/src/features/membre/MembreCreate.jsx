import  membreApi  from "../../api/membre";
import { GenericCreation } from "../../components/common/GenericCreation";
import { membreSchema, membreFields, membreConfig } from "./membreConfig";

const MembreCreate = () => {
  return (
    <GenericCreation
      modelName={membreConfig.modelName}
      apiCreate={membreApi.create}
      schema={membreSchema}
      fields={membreFields}
      navigateTo={membreConfig.navigateTo}
    />
  );
};

export default MembreCreate;