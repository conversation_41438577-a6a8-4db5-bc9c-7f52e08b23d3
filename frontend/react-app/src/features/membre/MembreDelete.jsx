import membreApi  from "../../api/membre";
import GenericDelete from "../../components/common/GenericDelete";
import { useNavigate, useParams } from "react-router-dom";

 const MembreDelete = () => {
    const navigate = useNavigate();
    const { id } = useParams();
    return (
      <GenericDelete ressource="membres" ressourceApi={membreApi} id={id} navigate={navigate} />
    );
};
export default MembreDelete;