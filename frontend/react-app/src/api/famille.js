// src/services/membresService.js
import createApiService from '../services/apiService';
import axios from 'axios';

const familleApi = createApiService('familles');

// Add custom endpoint to associate members with a family
familleApi.addMembers = async (familleId, memberIds) => {
  const response = await axios.post(`http://localhost:8000/api/familles/${familleId}/membres`, {
    memberIds
  });
  return response.data;
};

export default familleApi;
