// src/services/membresService.js
import createApiService from './../services/apiService';
import axios from 'axios';

const membreApi = createApiService('membres');

// Add custom endpoint(s)
membreApi.import = async (file) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await axios.post('http://localhost:8000/api/membres/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });

  return response.data;
};

export default membreApi;
