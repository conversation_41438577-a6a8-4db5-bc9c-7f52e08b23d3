<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        Schema::table('mambras', function (Blueprint $table) {
            $table->string('person_code')->unique()->change(); // adds unique index if not already
        });
        
        Schema::create('famille_mambra', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('famille_id')->nullable();
            $table->string('mambra_person_code');

            $table->foreign('famille_id')
                ->references('id')
                ->on('familles')
                ->nullOnDelete();

            $table->foreign('mambra_person_code')
                ->references('person_code')
                ->on('mambras')
                ->restrictOnDelete();

            $table->unique(['famille_id', 'mambra_person_code']); // avoid duplicates The combination of famille_id and mambra_person_code must be unique in the table.
        }); 

   
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('famille_mambra');
    }
};
