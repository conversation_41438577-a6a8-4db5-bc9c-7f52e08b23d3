<?php

use App\Models\Famille;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\KilasyController;
use App\Http\Controllers\MambraController;
use App\Http\Controllers\FamilleController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::prefix('membres')->controller(MambraController::class)->group(function () {
    Route::get('/', 'index');
    Route::post('/', 'store');
    Route::get('/{mambra}', 'show');
    Route::put('/{mambra}', 'update');
    Route::delete('/{mambra}', 'destroy');
    Route::post('/import', 'import');
});

Route::prefix('kilasys')->controller(KilasyController::class)->group(function () {
    Route::get('/', 'index');
    Route::post('/', 'store');
    Route::get('/{kilasy}', 'show');
    Route::put('/{kilasy}', 'update');
    Route::delete('/{kilasy}', 'destroy');
});


Route::prefix('familles')->controller(FamilleController::class)->group(function () {
    Route::get('/', 'index');
    Route::post('/', 'store');
    Route::get('/{famille}', 'show');
    Route::put('/{famille}', 'update');
    Route::delete('/{famille}', 'destroy');
    Route::post('/{famille}/membres', 'addMembers');
});

