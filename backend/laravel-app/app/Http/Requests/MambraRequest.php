<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MambraRequest extends FormRequest{

    public function authorize(){
        return true;
    }

    public function rules(){
        $rules = [
            'nom'                             => ['required', 'string', 'max:255'], 
            'prenom'                          => ['required', 'string', 'max:255'],
            'date_naissance'                  => ['nullable', 'date'],
            'sexe'                            => ['required', 'string', Rule::in(['masculin', 'feminin'])],
            'date_bapteme'                    => ['nullable', 'date'],
            'telephone'                       => ['nullable', 'string', 'max:255'],
            'situation_matrimoniale'          => ['nullable', 'string', Rule::in(['Célibataire', 'Mari<PERSON>(e)', 'Divorcé(e)', 'Veuf(ve)'])],
            'occupation'                      => ['nullable', 'string', 'max:255'],
            'observation'                     => ['nullable', 'string', 'max:255'],
            'person_code'                     => ['nullable', 'string', 'max:255'],
        ];

        return $rules;

    }


}



?>