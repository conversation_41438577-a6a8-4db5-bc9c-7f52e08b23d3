<?php
namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FamilleRequest extends FormRequest{

    public function authorize(){
        return true;
    }

    public function rules(){
        $rules = [
            'nom'                             => ['required', 'string', 'max:255'], 
            'adresse'                         => ['nullable', 'string', 'max:255'], 
            'observation'                     => ['nullable', 'string'],

        ];

        return $rules;

    }

}



?>