<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class MambraResource extends JsonResource{

    public function toArray($request){
        return [
            'id' => $this->id,
            'nom' => $this->nom,
            'prenom' => $this->prenom,
            'date_naissance' => $this->date_naissance,
            'sexe' => $this->sexe,
            'date_bapteme' => $this->date_bapteme,
            'telephone' => $this->telephone,
            'situation_matrimoniale' => $this->situation_matrimoniale,
            'occupation' => $this->occupation,
            'observation' => $this->observation,
            'person_code' => $this->person_code,
            'familles' => $this->whenLoaded('familles', function () {
                return FamilleResource::collection($this->familles);
            }),
        ];
    }


}




?>




