<?php

namespace App\Http\Resources;

use App\Models\Mambra;
use Illuminate\Http\Resources\Json\JsonResource;

class FamilleResource extends JsonResource{

    public function toArray($request){
        return [
            'id' => $this->id,
            'nom' => $this->nom,
            'adresse' => $this->adresse,
            'observation' => $this->observation,
            'mambras'=> $this->whenLoaded('mambras', function () {
                return MambraResource::collection($this->mambras);
            }),
        ];
    }


}




?>