<?php

namespace App\Http\Resources;

use App\Models\Mambra;
use Illuminate\Http\Resources\Json\JsonResource;

class FamilleResource extends JsonResource{

    public function toArray($request){
        return [
            'id' => $this->id,
            'nom' => $this->nom,
            'adresse' => $this->adresse,
            'observation' => $this->observation,
           // Fix: Always load mambras or provide empty collection
            'mambras' => $this->whenLoaded('mambras', function () {
                return $this->mambras->map(function ($mambra) {
                    return [
                        'person_code' => $mambra->person_code,
                        'nom' => $mambra->nom,
                        'prenom' => $mambra->prenom,
                        'sexe' => $mambra->sexe,
                    ];
                });
            }),
        ];
    }


}




?>