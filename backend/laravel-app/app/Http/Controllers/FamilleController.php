<?php

namespace App\Http\Controllers;

use App\Models\Mambra;
use App\Models\Famille;
use Illuminate\Http\Request;
use App\Http\Requests\FamilleRequest;
use App\Http\Resources\FamilleResource;
use Illuminate\Support\Facades\DB;

class FamilleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $familles = Famille::with('mambras')->get();
        return FamilleResource::collection($familles);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FamilleRequest $request)
    {
       $famille  = DB::transaction(function () use ($request) {
            $famille = new Famille ([
            'nom' => $request->get('nom'),
            'adresse' => $request->get('adresse'),
            'observation' => $request->get('observation'),
        ]);
            $famille->saveOrFail();
            return $famille;
        });

        return FamilleResource::make($famille);
    }


    /**
     * Display the specified resource.
     */
    public function show(Famille $famille)
    {
        $famille->load('mambras');
        return FamilleResource::make($famille);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Famille $famille)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Famille $famille)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Famille $famille)
    {
        //
    }

    /**
     * Add members to the specified famille using member IDs or person codes.
     */
    public function addMembers(Request $request, Famille $famille)
    {
        $requestData = $request->all();

        // Handle both memberIds (from frontend) and personCodes
        if (isset($requestData['memberIds'])) {
            $memberIds = $requestData['memberIds'];

            // Convert member IDs to person codes
            $mambras = Mambra::whereIn('id', $memberIds)->get();
            foreach ($mambras as $mambra) {
                $famille->addMembreByCode($mambra->person_code);
            }
        } elseif (isset($requestData['personCodes'])) {
            $personCodes = $requestData['personCodes'];

            foreach ($personCodes as $personCode) {
                $famille->addMembreByCode($personCode);
            }
        }

        $familles = Famille::with('mambras')->get();
        return FamilleResource::collection($familles);
    }



    /**
     * Example of how to use the relationships
     */
    public function addMemberToFamily(Request $request, $familleId)
    {
        $famille = Famille::find($familleId);
        $personCode = $request->input('person_code');

        if ($famille && $famille->addMembreByCode($personCode)) {
            return response()->json(['message' => 'Member added successfully']);
        }

        return response()->json(['error' => 'Failed to add member'], 400);
    }

    /**
     * Get all members of a family
     */
    public function getFamilyMembers($familleId)
    {
        $famille = Famille::with('mambras')->find($familleId);
        
        if (!$famille) {
            return response()->json(['error' => 'Family not found'], 404);
        }

        return response()->json([
            'famille' => $famille,
            'members' => $famille->mambras
        ]);
    }

    /**
     * Get all families of a member
     */
    public function getMemberFamilies($personCode)
    {
        $mambra = Mambra::where('person_code', $personCode)->with('familles')->first();
        
        if (!$mambra) {
            return response()->json(['error' => 'Member not found'], 404);
        }

        return response()->json([
            'member' => $mambra,
            'families' => $mambra->familles
        ]);
    }

    /**
     * Debug relationship
     */
    public function debugRelationship($familleId)
    {
        $famille = Famille::find($familleId);
        
        if (!$famille) {
            return response()->json(['error' => 'Family not found'], 404);
        }

        // Debug the relationship query
        $query = $famille->mambras()->getQuery();
        
        return response()->json([
            'famille' => $famille,
            'relationship_query' => $query->toSql(),
            'bindings' => $query->getBindings(),
            'members_count' => $famille->mambras()->count(),
            'members' => $famille->mambras()->get()
        ]);
    }
}
