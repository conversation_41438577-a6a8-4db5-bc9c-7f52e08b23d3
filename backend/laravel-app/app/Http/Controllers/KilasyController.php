<?php

namespace App\Http\Controllers;

use App\Http\Requests\KilasyRequest;
use App\Models\Kilasy;
use Illuminate\Support\Facades\DB;
use App\Http\Resources\KilasyResource;

class KilasyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $kilasy = Kilasy::all();
        return KilasyResource::collection($kilasy);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(KilasyRequest $request)
    {
        $c = DB::transaction(function () use ($request) {
            $kilasy = new Kilasy ([
            'nom' => $request->get('nom'),
            'libelle' => $request->get('libelle'),
            'description' => $request->get('description'),
        ]);
            $kilasy->saveOrFail();
            return $kilasy;
        });

        return KilasyResource::make($c);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(KilasyRequest $request, Kilasy $kilasy)
    {

        $kilasy->update($request->all());
        return KilasyResource::make($kilasy);

    }


    /**
     * Display the specified resource.
     */
    public function show(Kilasy $kilasy)
    {
        return KilasyResource::make($kilasy);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Kilasy $kilasy)
    {
        $kilasy->delete();
        $kilasys = Kilasy::all();
        return KilasyResource::collection($kilasys);

    }
}
