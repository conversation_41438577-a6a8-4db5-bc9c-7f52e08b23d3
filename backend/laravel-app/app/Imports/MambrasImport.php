<?php

namespace App\Imports;

use App\Models\Mambra;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Carbon\Carbon;

class MambrasImport implements ToModel, WithHeadingRow
{
    /**
     * @param array $row
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new Mambra([
            'person_code' => $row['person_code'] ?? null,
            'nom' => $row['nom'] ?? null,
            'prenom' => $row['nom_de_famille'] ?? null,
            'date_naissance' => !empty($row['date_naissance']) ? 
                $this->transformDate($row['date_naissance']) : null,
            'telephone' => $row['telephone'] ?? null,
            'sexe' => $row['sexe'] ?? null,
            'situation_matrimoniale' => $row['situation_matrimoniale'] ?? null,
            'occupation' => $row['occupation'] ?? null,
            'date_bapteme' => !empty($row['date_bapteme']) ? 
                $this->transformDate($row['date_bapteme']) : null,
            'observations' => $row['observations'] ?? null,
            'famille_id' =>  null,
        ]);
    }

    /**
     * Transform date from Excel format
     */
    private function transformDate($value)
    {
        try {
            if (is_numeric($value)) {
                // Excel date format
                return Carbon::createFromFormat('Y-m-d', gmdate('Y-m-d', ($value - 25569) * 86400));
            }
            
            // Try different date formats
            $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'd-m-Y'];
            foreach ($formats as $format) {
                try {
                    return Carbon::createFromFormat($format, $value);
                } catch (\Exception $e) {
                    continue;
                }
            }
            
            // If all fails, try Carbon parse
            return Carbon::parse($value);
            
        } catch (\Exception $e) {
            return null; // Return null if date can't be parsed
        }
    }
}