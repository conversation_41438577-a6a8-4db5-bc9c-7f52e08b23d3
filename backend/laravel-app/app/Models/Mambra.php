<?php
// App/Models/Mambra.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Mambra extends Model
{
    protected $table = 'mambras';
    
    protected $fillable = [
        'nom',
        'prenom',
        'date_naissance',
        'sexe',
        'date_bapteme',
        'telephone',
        'situation_matrimoniale',
        'occupation',
        'observation',
        'person_code',
    ];

    /**
     * Define the many-to-many relationship with <PERSON>amille using person_code
     */
    public function familles()
    {
        return $this->belongsToMany(
            Famille::class,          // Related model
            'famille_mambra',        // Pivot table name
            'mambra_person_code',    // Foreign key of current model in pivot table
            'famille_id',            // Foreign key of related model in pivot table
            'person_code',           // Local key of current model
            'id'                     // Local key of related model
        );
    }
}

