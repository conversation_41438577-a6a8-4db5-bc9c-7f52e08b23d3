<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Famille extends Model
{
    public $timestamps = false;
    protected $table = 'familles';
    
    protected $fillable = [
        'nom',
        'adresse',
        'observation'
    ];

    /**
     * Define the many-to-many relationship with Mambra using person_code
     */
    public function mambras()
    {
        return $this->hasMany(Mambra::class);    // Related model;
    }

   
}