<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Famille extends Model
{
    public $timestamps = false;
    protected $table = 'familles';
    
    protected $fillable = [
        'nom',
        'adresse',
        'observation'
    ];

    /**
     * Define the many-to-many relationship with Mambra using person_code
     */
    public function mambras()
    {
        return $this->belongsToMany(
            Mambra::class,           // Related model
            'famille_mambra',        // Pivot table name
            'famille_id',            // Foreign key of current model in pivot table
            'mambra_person_code',    // Foreign key of related model in pivot table
            'id',                    // Local key of current model
            'person_code'            // Local key of related model
        );
    }

    /**
     * Add a member to this famille using their person_code
     */
    public function addMembreByCode($personCode)
    {
        // Check if the person_code exists in mambras table
        if (!Mambra::where('person_code', $personCode)->exists()) {
            return false;
        }

        // Check if relationship already exists
        if ($this->mambras()->where('person_code', $personCode)->exists()) {
            return true; // Already attached
        }

        // Attach using the person_code directly
        $this->mambras()->attach($personCode);
        return true;
    }
}